import type { PropsWithChildren } from "react";

import type { Metada<PERSON> } from "next";

import Script from "next/script";

import { RootHeader } from "~/app/(web)/_components/root-header";
import { cn } from "~/lib/utils";
import { RootFooter } from "./_components/root-footer";

export const metadata: Metadata = {
  title: "EcoBuiltConnect - Marketplace",
  description: "EcoBuiltConnect - Marketplace",
};

export default function PublicLayout({
  children,
}: Readonly<PropsWithChildren>) {
  return (
    <>
      <RootHeader />
      <main className={cn("relative")}>{children}</main>
      <RootFooter />

      <Script
        src="//code.tidio.co/kqf9guvqo5gtjv8urt7mcen7j2hqtcmc.js"
        strategy="afterInteractive"
        async
      />
    </>
  );
}
